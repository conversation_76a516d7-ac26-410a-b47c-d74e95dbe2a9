local GameNpc = {}
local module = require "module.module"

GameNpc.State = {
    Sleep = 1,
    Waiting = 2,
    BeHit = 3,
    Qte = 4,
    End = 5,
}

GameNpc.NowState = GameNpc.State.Sleep

function GameNpc:Init()
    module.event.evt_update:Register(GameNpc.Update, self)
end

-- qte是否成功
GameNpc.qtesuccess = false

function GameNpc:Update(deltatime)
    if GameNpc.NowState == GameNpc.State.Waiting then
        if self.waitingtime == nil then
            self.waitingtime = 0
        end
        self.waitingtime = self.waitingtime - deltatime
        -- 如果到了思考时间
        if self.waitingtime <= 0 then
            GameNpc:UpdateWaiting()
        end
        
    elseif GameNpc.NowState == GameNpc.State.Qte then
        if self.nextqtetime == nil then
            self.nextqtetime = 0
        end
        self.nextqtetime = self.nextqtetime - deltatime
        if self.nextqtetime <= 0 then
            GameNpc:UpdateQte()
        end

        if self.qtethinktime == nil then
            self.qtethinktime = self.nextqtetime
        end
        self.qtethinktime = self.qtethinktime - deltatime

        if self.qtethinktime <= 0 then
            -- 判断这次qte是否成功
            local success = GameNpc:QTESuccess()
            if success then
                -- 成功,继续下个循环
                GameNpc.qtesuccess = true
                
            else
                -- 失败
                print("QTE Failed")
                GameNpc.qtesuccess = false
                self:QTEFailAction()
            end
        end
        if self.qtethinktime <= 0 then
            self:QTEFinishAction()
        end
        
    end
    
end

---- 入口 ------
function GameNpc:StartPlay()
    GameNpc.NowState = GameNpc.State.Waiting
    self.waitingtime = 0
    self.qtecount = 0
    self.nextqtetime = 0
    self.qtethinktime = 0
end

function GameNpc:UpdateWaiting()
    -- 如果到思考时间了
    self.nowDirection = GameNpc:RandomDirection()
    self.waitingtime = self.waitingtime + GameNpc:GetNextThinkTime()
    self:ActionDefence()
end

-- 进行防御
function GameNpc:ActionDefence()
    print("GameNpc:ActionDefence"..self.nowDirection)
end

function GameNpc:UpdateQte()
    if self.qtecount > 0 then
        -- 先判断上一次是否是失败的
        if self.qtesuccess == false then
            self:QTEFailAction()
            return
        end
        self.nextqtetime = self.nextqtetime + 1 -- todo 这个要写一下
        -- 先qte表现 注意，这里不知道qte到底是固定时间还是咋样
        local qtedirection = self:QTEOnce()
    end
end

function GameNpc:GetNextThinkTime()
    local cfg = {}  -- thinktime
    local mintime = 1
    local maxtime = 2
    local time = math.random(mintime, maxtime)
    return time
end

function GameNpc:RandomDirection()
    local cfg = {}
    local direction = {1, 2, 3, 4}
    local index = math.random(1, #direction)
    return direction[index]
    -- 按右键闭眼绷紧肌肉0.5秒进行防御。就不做了先
end


-------- 被打击   -------------------------
-- 返回 是否进入眩晕抵抗
function GameNpc:BeHit(direction,force)

    GameNpc.NowState = GameNpc.State.BeHit
    -- 看看这次是不是读指令
    local cheatrate = 0.5 -- todo 这里读一下表  cheatrate
    local cheat = math.random()
    if cheat < cheatrate then
        -- 读指令
        self.nowDirection = direction
    else
        self.nowDirection = GameNpc:RandomDirection()
    end

    -- 判断成功or 失败
    if self.nowDirection == direction then
        -- 成功
        print("npc 阻挡成功")
        self:BeginQTE(force)
        return true
    else
        self:GameOver()
        -- 失败
        return false
    end
end

function GameNpc:GameOver()
    print("破防")
    GameNpc.NowState = GameNpc.State.End
end

function GameNpc:RandomQteDirection()
    local cfg = {}
    local direction = {1, 2, 3, 4}
    local index = math.random(1, #direction)
    return direction[index]
end

------------------- 进入qte -------------------
function GameNpc:BeginQTE(force)
    GameNpc.NowState = GameNpc.State.Qte

    self.qtesuccess = true -- 先默认成功

    -- 根据force决定有几个qte。先写死了
    self.qtecount = 5
    print("眩晕抵抗环节")
end

-- 执行一次qte表现
function GameNpc:QTEOnce()
    local qtedirection = GameNpc:RandomQteDirection()
    print("表现qte 方向" .. qtedirection)
    return qtedirection
end


----------- 通知成功还是失败 -------------------
-- 判断这次qte是否成功
function GameNpc:QTESuccess()
    local failrate = 0.01 -- todo 这里读一下表   qtefailrate
    local fail = math.random()

    if fail > failrate then
        -- 成功
        return true
    end
    return false
end

-- qte失败的表现
function GameNpc:QTEFailAction()
    GameNpc.NowState = GameNpc.State.End
    print("GameNpc:QTEFailAction")
end

-- qte成功
function GameNpc:QTESuccessAction()
end

--- qte彻底成功 -----------------------
function GameNpc:QTEFinishAction()
    GameNpc.NowState = GameNpc.State.End
    print("GameNpc:QTEFinishAction")
    
end

return GameNpc